#ifndef ENHANCED_GUI_MANAGER_H
#define ENHANCED_GUI_MANAGER_H

#include "DeploymentEngine.h"
#include "CredentialManager.h"
#include "NetworkDiscovery.h"
#include "PackageManifest.h"
#include <memory>
#include <vector>
#include <map>
#include <string>
#include <functional>

#ifdef _WIN32
#include <windows.h>
#include <commctrl.h>
#endif

// GUI component IDs
enum GuiComponentId {
    // Main window components
    ID_MAIN_WINDOW = 1000,
    ID_MENU_BAR,
    ID_STATUS_BAR,
    ID_TOOLBAR,
    
    // Menu items
    ID_MENU_FILE = 1100,
    ID_MENU_FILE_NEW_PACKAGE,
    ID_MENU_FILE_OPEN_PACKAGE,
    ID_MENU_FILE_IMPORT_TARGETS,
    ID_MENU_FILE_EXPORT_RESULTS,
    ID_MENU_FILE_EXIT,
    
    ID_MENU_EDIT = 1200,
    ID_MENU_EDIT_CREDENTIALS,
    ID_MENU_EDIT_PREFERENCES,
    
    ID_MENU_DEPLOY = 1300,
    ID_MENU_DEPLOY_NOW,
    ID_MENU_DEPLOY_SCHEDULE,
    ID_MENU_DEPLOY_CANCEL,
    ID_MENU_DEPLOY_RETRY,
    
    ID_MENU_TOOLS = 1400,
    ID_MENU_TOOLS_NETWORK_SCAN,
    ID_MENU_TOOLS_TEST_CONNECTIVITY,
    ID_MENU_TOOLS_SYSTEM_INFO,
    ID_MENU_TOOLS_CLEANUP,
    
    ID_MENU_HELP = 1500,
    ID_MENU_HELP_ABOUT,
    ID_MENU_HELP_DOCUMENTATION,
    
    // Toolbar buttons
    ID_TOOLBAR_NEW_PACKAGE = 1600,
    ID_TOOLBAR_DEPLOY,
    ID_TOOLBAR_CANCEL,
    ID_TOOLBAR_REFRESH,
    ID_TOOLBAR_SETTINGS,
    
    // Main panels
    ID_PANEL_PACKAGES = 2000,
    ID_PANEL_TARGETS,
    ID_PANEL_DEPLOYMENTS,
    ID_PANEL_LOGS,
    
    // Package panel
    ID_PACKAGE_LIST = 2100,
    ID_PACKAGE_DETAILS,
    ID_PACKAGE_STEPS,
    ID_BTN_NEW_PACKAGE,
    ID_BTN_EDIT_PACKAGE,
    ID_BTN_DELETE_PACKAGE,
    ID_BTN_DUPLICATE_PACKAGE,
    
    // Target panel
    ID_TARGET_LIST = 2200,
    ID_TARGET_GROUPS,
    ID_BTN_ADD_TARGET,
    ID_BTN_SCAN_NETWORK,
    ID_BTN_IMPORT_TARGETS,
    ID_BTN_TEST_CONNECTIVITY,
    ID_EDIT_NETWORK_RANGE,
    
    // Deployment panel
    ID_DEPLOYMENT_LIST = 2300,
    ID_DEPLOYMENT_PROGRESS,
    ID_DEPLOYMENT_DETAILS,
    ID_BTN_DEPLOY_NOW,
    ID_BTN_SCHEDULE_DEPLOY,
    ID_BTN_CANCEL_DEPLOY,
    ID_BTN_RETRY_DEPLOY,
    
    // Log panel
    ID_LOG_LIST = 2400,
    ID_LOG_FILTER,
    ID_BTN_CLEAR_LOGS,
    ID_BTN_EXPORT_LOGS,
    
    // Dialog components
    ID_DIALOG_CREDENTIALS = 3000,
    ID_DIALOG_PACKAGE_EDITOR,
    ID_DIALOG_DEPLOYMENT_SCHEDULE,
    ID_DIALOG_NETWORK_SCAN,
    ID_DIALOG_PREFERENCES,
    ID_DIALOG_ABOUT
};

// GUI panel types
enum class PanelType {
    PACKAGES,
    TARGETS,
    DEPLOYMENTS,
    LOGS
};

// Target information for GUI display
struct GuiTargetInfo {
    std::string hostname;
    std::string ipAddress;
    std::string status;        // Online, Offline, Unknown
    std::string osVersion;
    std::string architecture;
    std::string lastSeen;
    bool isSelected = false;
};

// Package information for GUI display
struct GuiPackageInfo {
    std::string name;
    std::string version;
    std::string description;
    std::string filePath;
    int stepCount = 0;
    std::string lastModified;
    bool isValid = true;
};

// Deployment information for GUI display
struct GuiDeploymentInfo {
    std::string deploymentId;
    std::string packageName;
    std::string targetHost;
    std::string status;
    std::string startTime;
    std::string endTime;
    int progressPercentage = 0;
    std::string currentStep;
};

// Enhanced GUI Manager class
class EnhancedGuiManager {
public:
    EnhancedGuiManager();
    ~EnhancedGuiManager();
    
    // Initialize the GUI manager
    bool initialize();
    
    // Run the main GUI loop
    int run();
    
    // Shutdown and cleanup
    void shutdown();
    
    // Set deployment engine
    void setDeploymentEngine(std::shared_ptr<DeploymentEngine> engine);
    
    // Set credential manager
    void setCredentialManager(std::shared_ptr<CredentialManager> credManager);
    
    // Set network discovery
    void setNetworkDiscovery(std::shared_ptr<NetworkDiscovery> netDiscovery);

private:
    // Window creation and management
    bool createMainWindow();
    bool createMenuBar();
    bool createToolbar();
    bool createStatusBar();
    bool createPanels();
    
    // Panel management
    void showPanel(PanelType panel);
    void refreshPanel(PanelType panel);
    void resizePanels();
    
    // Package management UI
    void refreshPackageList();
    void showPackageDetails(const std::string& packagePath);
    void createNewPackage();
    void editPackage(const std::string& packagePath);
    void deletePackage(const std::string& packagePath);
    
    // Target management UI
    void refreshTargetList();
    void addTarget(const std::string& hostname);
    void scanNetwork();
    void testConnectivity();
    void importTargets();
    
    // Deployment management UI
    void refreshDeploymentList();
    void deployNow();
    void scheduleDeployment();
    void cancelDeployment();
    void retryDeployment();
    void showDeploymentDetails(const std::string& deploymentId);
    
    // Log management UI
    void refreshLogList();
    void clearLogs();
    void exportLogs();
    void filterLogs(const std::string& filter);
    
    // Dialog management
    bool showCredentialsDialog();
    bool showPackageEditorDialog(const std::string& packagePath = "");
    bool showScheduleDialog();
    bool showNetworkScanDialog();
    bool showPreferencesDialog();
    void showAboutDialog();
    
    // Event handlers
    void onMenuCommand(int commandId);
    void onToolbarCommand(int commandId);
    void onListSelection(int listId, int selectedIndex);
    void onButtonClick(int buttonId);
    
    // Progress callbacks
    void onDeploymentProgress(const DeploymentProgress& progress);
    void onDeploymentComplete(const DeploymentResult& result);
    
    // Utility functions
    void updateStatusBar(const std::string& message);
    void logMessage(const std::string& level, const std::string& message);
    std::string formatTime(const std::chrono::system_clock::time_point& timePoint);
    void enableControls(bool enabled);
    
    // Data management
    void loadPackages();
    void loadTargets();
    void loadDeploymentHistory();
    void saveConfiguration();
    void loadConfiguration();
    
    // Platform-specific window procedure
#ifdef _WIN32
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    LRESULT handleMessage(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
#endif
    
    // Member variables
    std::shared_ptr<DeploymentEngine> deploymentEngine_;
    std::shared_ptr<CredentialManager> credentialManager_;
    std::shared_ptr<NetworkDiscovery> networkDiscovery_;
    
    // GUI state
    bool isInitialized_;
    PanelType currentPanel_;
    
    // Window handles (platform specific)
#ifdef _WIN32
    HWND mainWindow_;
    HWND menuBar_;
    HWND toolbar_;
    HWND statusBar_;
    HWND packagePanel_;
    HWND targetPanel_;
    HWND deploymentPanel_;
    HWND logPanel_;
    
    // Control handles
    HWND packageList_;
    HWND targetList_;
    HWND deploymentList_;
    HWND logList_;
#endif
    
    // Data storage
    std::vector<GuiPackageInfo> packages_;
    std::vector<GuiTargetInfo> targets_;
    std::vector<GuiDeploymentInfo> deployments_;
    std::vector<std::string> logMessages_;
    
    // Configuration
    std::map<std::string, std::string> guiConfig_;
    
    // Selection state
    std::vector<int> selectedPackages_;
    std::vector<int> selectedTargets_;
    std::vector<int> selectedDeployments_;
    
    // Threading
    std::mutex guiMutex_;
};

// Utility functions for GUI operations
namespace GuiUtils {
    // Convert system time to display string
    std::string timeToString(const std::chrono::system_clock::time_point& time);
    
    // Format file size for display
    std::string formatFileSize(size_t bytes);
    
    // Format duration for display
    std::string formatDuration(const std::chrono::seconds& duration);
    
    // Get status color for display
    int getStatusColor(const std::string& status);
    
    // Show message box
    void showMessage(const std::string& title, const std::string& message, bool isError = false);
    
    // Show confirmation dialog
    bool showConfirmation(const std::string& title, const std::string& message);
    
    // Browse for file
    std::string browseForFile(const std::string& filter = "", bool save = false);
    
    // Browse for folder
    std::string browseForFolder();
}

#endif // ENHANCED_GUI_MANAGER_H
