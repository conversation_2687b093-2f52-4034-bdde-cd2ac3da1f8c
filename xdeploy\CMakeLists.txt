cmake_minimum_required(VERSION 3.10)
project(xdeploy VERSION 1.0 LANGUAGES CXX)

# --- Dependencies ---
include(FetchContent)


set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)
add_definitions(-DWIN32_LEAN_AND_MEAN -DUNICODE -D_UNICODE)

# Add include directory
include_directories(include)

# Ensure version.h is generated and included
include_directories("${PROJECT_BINARY_DIR}/include")

# Add source files to create the executable

add_executable(xdeploy
    src/main.cpp
    src/NetworkDiscovery.cpp
    src/PackageCreator.cpp
    src/Deployer.cpp
    src/RemoteSystem.cpp
    src/DeploymentOrchestrator.cpp
    src/GuiManager.cpp
    src/CredentialManager.cpp
)

# Link against Win32 GUI libraries
target_link_libraries(xdeploy PRIVATE user32 gdi32 comctl32)

# Enable testing
enable_testing()

# --- Testing ---
if(CMAKE_PROJECT_NAME STREQUAL "xdeploy" OR PROJECT_NAME STREQUAL "xdeploy")
    message(STATUS "Configuring tests for xdeploy...")

    add_executable(test_manifest_parser
        tests/test_deployer_manifest_parser.cpp
        src/Deployer.cpp
        src/DeploymentOrchestrator.cpp # Added: Deployer uses DeploymentOrchestrator
        src/RemoteSystem.cpp
        # src/PackageManifest.cpp # If it existed and was needed
    )

    # Test executable needs to find Deployer.h, PackageManifest.h
    # Deployer.cpp needs RemoteCommander.h, PackageManifest.h
    # The main include_directories(include) should cover these.
    # If Deployer.cpp or other linked sources needed their own specific includes not covered globally,
    # target_include_directories(test_manifest_parser PRIVATE ...) would be used.
    # We also need to link against stdc++fs if on an older GCC version, but CMAKE_CXX_STANDARD 17 usually handles it.
    # For simplicity, assuming global include_directories is sufficient.
    # If linking issues arise, link xdeploy's sources or object files if necessary,
    # or ensure all necessary .cpp files are part of the test_manifest_parser sources.
    # For now, Deployer.cpp is the only direct dependency from src/.

    target_include_directories(test_manifest_parser PRIVATE include)

    add_test(NAME ManifestParserTest COMMAND test_manifest_parser)

    message(STATUS "CTest ManifestParserTest added.")

    add_executable(test_utils
        tests/test_utils.cpp
        # PackageManifest.h and Credentials.h are header-only
    )
    target_include_directories(test_utils PRIVATE include)
    add_test(NAME UtilsTest COMMAND test_utils)
    message(STATUS "CTest UtilsTest added.")
else()
    message(STATUS "Skipping test configuration for non-main project.")
endif()
