^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKEFILES\534F533D09F5773522C365AD767FEF07\GLFW-POPULATE-MKDIR.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/tmp/glfw-populate-mkdirs.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/Debug/glfw-populate-mkdir"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKEFILES\534F533D09F5773522C365AD767FEF07\GLFW-POPULATE-DOWNLOAD.RULE
setlocal
cd "C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps"
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/download-glfw-populate.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/verify-glfw-populate.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/extract-glfw-populate.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/Debug/glfw-populate-download"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKEFILES\534F533D09F5773522C365AD767FEF07\GLFW-POPULATE-UPDATE.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/Debug/glfw-populate-update"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKEFILES\534F533D09F5773522C365AD767FEF07\GLFW-POPULATE-PATCH.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/Debug/glfw-populate-patch"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKEFILES\534F533D09F5773522C365AD767FEF07\GLFW-POPULATE-CONFIGURE.RULE
setlocal
cd "C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build"
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/Debug/glfw-populate-configure"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKEFILES\534F533D09F5773522C365AD767FEF07\GLFW-POPULATE-BUILD.RULE
setlocal
cd "C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build"
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/Debug/glfw-populate-build"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKEFILES\534F533D09F5773522C365AD767FEF07\GLFW-POPULATE-INSTALL.RULE
setlocal
cd "C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build"
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/Debug/glfw-populate-install"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKEFILES\534F533D09F5773522C365AD767FEF07\GLFW-POPULATE-TEST.RULE
setlocal
cd "C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build"
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/Debug/glfw-populate-test"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKEFILES\25E4CD7F1AD46EDECEBBADFC94009488\GLFW-POPULATE-COMPLETE.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/CMakeFiles/Debug"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/CMakeFiles/Debug/glfw-populate-complete"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/glfw-populate-prefix/src/glfw-populate-stamp/Debug/glfw-populate-done"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKEFILES\87EDBB7E1A65EDB810A2F5D6608D37FE\GLFW-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE - CONCENTRIX CORPORATION\DESKTOP\APPS-JULES_WIP_7542290459048296198\BUILD\_DEPS\GLFW-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild" "-BC:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild" --check-stamp-file "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-subbuild/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
