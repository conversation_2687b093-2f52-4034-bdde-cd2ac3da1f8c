#ifndef REPORTING_MANAGER_H
#define REPORTING_MANAGER_H

#include "DeploymentEngine.h"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <chrono>
#include <filesystem>
#include <functional>

namespace fs = std::filesystem;

// Report types
enum class ReportType {
    DEPLOYMENT_SUMMARY,
    DEPLOYMENT_DETAILED,
    PACKAGE_USAGE,
    TARGET_STATUS,
    ERROR_ANALYSIS,
    PERFORMANCE_METRICS,
    SECURITY_AUDIT,
    INVENTORY_REPORT,
    CUSTOM
};

// Report formats
enum class ReportFormat {
    HTML,
    PDF,
    CSV,
    JSON,
    XML,
    EXCEL
};

// Time period for reports
enum class ReportPeriod {
    LAST_HOUR,
    LAST_DAY,
    LAST_WEEK,
    LAST_MONTH,
    LAST_QUARTER,
    LAST_YEAR,
    CUSTOM_RANGE,
    ALL_TIME
};

// Report filter criteria
struct ReportFilter {
    ReportPeriod period = ReportPeriod::LAST_WEEK;
    std::chrono::system_clock::time_point startDate;
    std::chrono::system_clock::time_point endDate;
    std::vector<std::string> targetHosts;
    std::vector<std::string> packageNames;
    std::vector<std::string> deploymentStatuses;
    std::vector<std::string> userNames;
    std::string searchTerm;
    int maxResults = 1000;
    bool includeSuccessful = true;
    bool includeFailed = true;
    bool includeCancelled = false;
};

// Report data structures
struct DeploymentSummaryData {
    int totalDeployments = 0;
    int successfulDeployments = 0;
    int failedDeployments = 0;
    int cancelledDeployments = 0;
    double successRate = 0.0;
    std::chrono::seconds averageDuration{0};
    std::map<std::string, int> deploymentsByPackage;
    std::map<std::string, int> deploymentsByTarget;
    std::map<std::string, int> deploymentsByStatus;
    std::map<std::string, int> deploymentsByDay;
};

struct PackageUsageData {
    std::string packageName;
    std::string packageVersion;
    int totalDeployments = 0;
    int successfulDeployments = 0;
    int failedDeployments = 0;
    double successRate = 0.0;
    std::chrono::system_clock::time_point firstDeployment;
    std::chrono::system_clock::time_point lastDeployment;
    std::vector<std::string> targetHosts;
    std::map<std::string, int> errorCounts;
};

struct TargetStatusData {
    std::string hostname;
    std::string ipAddress;
    std::string status;
    int totalDeployments = 0;
    int successfulDeployments = 0;
    int failedDeployments = 0;
    std::chrono::system_clock::time_point lastDeployment;
    std::chrono::system_clock::time_point lastSeen;
    std::vector<std::string> installedPackages;
    std::map<std::string, std::string> systemInfo;
};

struct ErrorAnalysisData {
    std::string errorType;
    std::string errorMessage;
    int occurrenceCount = 0;
    std::vector<std::string> affectedTargets;
    std::vector<std::string> affectedPackages;
    std::chrono::system_clock::time_point firstOccurrence;
    std::chrono::system_clock::time_point lastOccurrence;
    std::string suggestedResolution;
};

struct PerformanceMetricsData {
    double averageDeploymentTime = 0.0;
    double medianDeploymentTime = 0.0;
    double minDeploymentTime = 0.0;
    double maxDeploymentTime = 0.0;
    int deploymentsPerHour = 0;
    int deploymentsPerDay = 0;
    std::map<std::string, double> averageTimeByPackage;
    std::map<std::string, double> averageTimeByTarget;
    std::map<int, int> deploymentTimeDistribution;
};

// Report generation configuration
struct ReportConfig {
    std::string title;
    std::string description;
    std::string author;
    std::string organization;
    bool includeCharts = true;
    bool includeDetails = true;
    bool includeSummary = true;
    bool includeRecommendations = true;
    std::string logoPath;
    std::string templatePath;
    std::map<std::string, std::string> customFields;
};

// Chart data for reports
struct ChartData {
    std::string title;
    std::string type; // pie, bar, line, area
    std::vector<std::string> labels;
    std::vector<double> values;
    std::map<std::string, std::string> colors;
    std::string xAxisLabel;
    std::string yAxisLabel;
};

// Report generation result
struct ReportResult {
    bool success = false;
    std::string filePath;
    std::string errorMessage;
    size_t fileSizeBytes = 0;
    std::chrono::system_clock::time_point generationTime;
    std::chrono::seconds generationDuration{0};
};

// Main reporting manager class
class ReportingManager {
public:
    ReportingManager();
    ~ReportingManager();
    
    // Initialize reporting manager
    bool initialize(std::shared_ptr<DeploymentEngine> deploymentEngine);
    
    // Generate reports
    ReportResult generateReport(ReportType type, ReportFormat format, 
                               const ReportFilter& filter = {},
                               const ReportConfig& config = {},
                               const fs::path& outputPath = "");
    
    // Generate custom report
    ReportResult generateCustomReport(const std::string& templatePath,
                                     const ReportFilter& filter,
                                     const ReportConfig& config,
                                     ReportFormat format,
                                     const fs::path& outputPath = "");
    
    // Get available report templates
    std::vector<std::string> getAvailableTemplates();
    
    // Schedule automatic report generation
    std::string scheduleReport(ReportType type, ReportFormat format,
                              const ReportFilter& filter,
                              const ReportConfig& config,
                              const std::chrono::system_clock::time_point& scheduledTime,
                              const fs::path& outputDirectory);
    
    // Schedule recurring report generation
    std::string scheduleRecurringReport(ReportType type, ReportFormat format,
                                       const ReportFilter& filter,
                                       const ReportConfig& config,
                                       const std::chrono::minutes& interval,
                                       const fs::path& outputDirectory);
    
    // Cancel scheduled report
    bool cancelScheduledReport(const std::string& scheduleId);
    
    // Get scheduled reports
    std::vector<std::string> getScheduledReports();
    
    // Data collection methods
    DeploymentSummaryData getDeploymentSummary(const ReportFilter& filter);
    std::vector<PackageUsageData> getPackageUsage(const ReportFilter& filter);
    std::vector<TargetStatusData> getTargetStatus(const ReportFilter& filter);
    std::vector<ErrorAnalysisData> getErrorAnalysis(const ReportFilter& filter);
    PerformanceMetricsData getPerformanceMetrics(const ReportFilter& filter);
    
    // Chart generation
    std::vector<ChartData> generateCharts(ReportType type, const ReportFilter& filter);
    
    // Export data
    bool exportToCSV(const std::vector<DeploymentResult>& data, const fs::path& filePath);
    bool exportToJSON(const std::vector<DeploymentResult>& data, const fs::path& filePath);
    bool exportToXML(const std::vector<DeploymentResult>& data, const fs::path& filePath);
    
    // Report history management
    std::vector<std::string> getReportHistory();
    bool deleteReport(const std::string& reportPath);
    void cleanupOldReports(int maxAgedays = 30);
    
    // Template management
    bool createReportTemplate(const std::string& templateName, const std::string& templateContent);
    bool updateReportTemplate(const std::string& templateName, const std::string& templateContent);
    bool deleteReportTemplate(const std::string& templateName);
    std::string getReportTemplate(const std::string& templateName);
    
    // Configuration
    void setDefaultOutputDirectory(const fs::path& directory);
    fs::path getDefaultOutputDirectory() const;
    
    void setReportRetentionDays(int days);
    int getReportRetentionDays() const;

private:
    // Report generation methods
    ReportResult generateDeploymentSummaryReport(const ReportFilter& filter, 
                                                const ReportConfig& config,
                                                ReportFormat format,
                                                const fs::path& outputPath);
    
    ReportResult generateDeploymentDetailedReport(const ReportFilter& filter,
                                                 const ReportConfig& config,
                                                 ReportFormat format,
                                                 const fs::path& outputPath);
    
    ReportResult generatePackageUsageReport(const ReportFilter& filter,
                                           const ReportConfig& config,
                                           ReportFormat format,
                                           const fs::path& outputPath);
    
    ReportResult generateTargetStatusReport(const ReportFilter& filter,
                                           const ReportConfig& config,
                                           ReportFormat format,
                                           const fs::path& outputPath);
    
    ReportResult generateErrorAnalysisReport(const ReportFilter& filter,
                                            const ReportConfig& config,
                                            ReportFormat format,
                                            const fs::path& outputPath);
    
    // Format-specific generators
    bool generateHTMLReport(const std::string& content, const fs::path& outputPath);
    bool generatePDFReport(const std::string& htmlContent, const fs::path& outputPath);
    bool generateCSVReport(const std::vector<std::vector<std::string>>& data, const fs::path& outputPath);
    bool generateJSONReport(const std::string& jsonContent, const fs::path& outputPath);
    bool generateXMLReport(const std::string& xmlContent, const fs::path& outputPath);
    
    // Template processing
    std::string processTemplate(const std::string& templateContent, 
                               const std::map<std::string, std::string>& variables);
    
    // Data filtering and aggregation
    std::vector<DeploymentResult> filterDeploymentResults(const ReportFilter& filter);
    std::map<std::string, std::string> calculateStatistics(const std::vector<DeploymentResult>& results);
    
    // Utility methods
    std::string generateReportFileName(ReportType type, ReportFormat format);
    fs::path getOutputPath(const fs::path& requestedPath, const std::string& fileName);
    std::string formatTimeRange(const ReportFilter& filter);
    std::string getReportTypeString(ReportType type);
    std::string getFormatExtension(ReportFormat format);
    
    // Scheduled report worker
    void scheduledReportWorker();
    
    std::shared_ptr<DeploymentEngine> deploymentEngine_;
    fs::path defaultOutputDirectory_;
    fs::path templateDirectory_;
    int reportRetentionDays_;
    
    // Scheduled reports
    std::map<std::string, std::function<void()>> scheduledReports_;
    std::unique_ptr<std::thread> schedulerThread_;
    std::atomic<bool> stopScheduler_;
    
    // Thread safety
    mutable std::mutex reportMutex_;
    
    bool isInitialized_;
};

// Report utility functions
namespace ReportUtils {
    // Time formatting for reports
    std::string formatDateTime(const std::chrono::system_clock::time_point& time);
    std::string formatDuration(const std::chrono::seconds& duration);
    std::string formatFileSize(size_t bytes);
    
    // Statistical calculations
    double calculateSuccessRate(int successful, int total);
    double calculateAverage(const std::vector<double>& values);
    double calculateMedian(std::vector<double> values);
    double calculateStandardDeviation(const std::vector<double>& values);
    
    // Color generation for charts
    std::string generateColor(int index);
    std::vector<std::string> generateColorPalette(int count);
    
    // HTML/CSS utilities
    std::string escapeHTML(const std::string& text);
    std::string generateCSS();
    std::string generateJavaScript();
}

#endif // REPORTING_MANAGER_H
