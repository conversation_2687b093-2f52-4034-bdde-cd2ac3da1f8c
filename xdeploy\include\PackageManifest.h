#ifndef PACKAGE_MANIFEST_H
#define PACKAGE_MANIFEST_H

#include <string>
#include <vector>
#include <filesystem>
#include <map>
#include <algorithm> // For string to upper for stringToPackageStepType
#include <cctype>    // For std::toupper

namespace fs = std::filesystem;

struct StagedFile {
    fs::path sourceInPackage;
    fs::path destinationInStaging;
};

enum class PackageStepType {
    UNKNOWN,
    INSTALL,           // Install MSI, EXE, or other installer
    COMMAND,           // Run command line executable
    POWERSHELL,        // Run PowerShell script
    BATCH,             // Run batch file
    FILE_COPY,         // Copy files to target system
    REGISTRY,          // Registry modifications
    SERVICE,           // Service operations (start, stop, install, uninstall)
    REBOOT,            // System reboot
    CONDITION,         // Conditional execution based on system state
    WAIT,              // Wait for specified time or condition
    DOWNLOAD,          // Download files from URL
    EXTRACT,           // Extract archives (ZIP, 7Z, etc.)
    ENVIRONMENT,       // Set environment variables
    SHORTCUT,          // Create desktop/start menu shortcuts
    UNINSTALL          // Uninstall software
};

inline std::string packageStepTypeToString(PackageStepType type) {
    switch (type) {
        case PackageStepType::INSTALL: return "INSTALL";
        case PackageStepType::COMMAND: return "COMMAND";
        case PackageStepType::POWERSHELL: return "POWERSHELL";
        case PackageStepType::BATCH: return "BATCH";
        case PackageStepType::FILE_COPY: return "FILE_COPY";
        case PackageStepType::REGISTRY: return "REGISTRY";
        case PackageStepType::SERVICE: return "SERVICE";
        case PackageStepType::REBOOT: return "REBOOT";
        case PackageStepType::CONDITION: return "CONDITION";
        case PackageStepType::WAIT: return "WAIT";
        case PackageStepType::DOWNLOAD: return "DOWNLOAD";
        case PackageStepType::EXTRACT: return "EXTRACT";
        case PackageStepType::ENVIRONMENT: return "ENVIRONMENT";
        case PackageStepType::SHORTCUT: return "SHORTCUT";
        case PackageStepType::UNINSTALL: return "UNINSTALL";
        default: return "UNKNOWN";
    }
}

inline PackageStepType stringToPackageStepType(std::string s) { // Pass by value to modify
    std::transform(s.begin(), s.end(), s.begin(), [](unsigned char c){ return std::toupper(c); });
    if (s == "INSTALL") return PackageStepType::INSTALL;
    if (s == "COMMAND") return PackageStepType::COMMAND;
    if (s == "POWERSHELL") return PackageStepType::POWERSHELL;
    if (s == "BATCH") return PackageStepType::BATCH;
    if (s == "FILE_COPY") return PackageStepType::FILE_COPY;
    if (s == "REGISTRY") return PackageStepType::REGISTRY;
    if (s == "SERVICE") return PackageStepType::SERVICE;
    if (s == "REBOOT") return PackageStepType::REBOOT;
    if (s == "CONDITION") return PackageStepType::CONDITION;
    if (s == "WAIT") return PackageStepType::WAIT;
    if (s == "DOWNLOAD") return PackageStepType::DOWNLOAD;
    if (s == "EXTRACT") return PackageStepType::EXTRACT;
    if (s == "ENVIRONMENT") return PackageStepType::ENVIRONMENT;
    if (s == "SHORTCUT") return PackageStepType::SHORTCUT;
    if (s == "UNINSTALL") return PackageStepType::UNINSTALL;
    return PackageStepType::UNKNOWN;
}

// Condition types for conditional execution
enum class ConditionType {
    ALWAYS,                    // Always execute
    OS_VERSION,               // Check OS version
    ARCHITECTURE,             // Check system architecture (x86, x64)
    REGISTRY_EXISTS,          // Check if registry key/value exists
    FILE_EXISTS,              // Check if file exists
    PROCESS_RUNNING,          // Check if process is running
    SERVICE_EXISTS,           // Check if service exists
    DISK_SPACE,               // Check available disk space
    MEMORY,                   // Check available memory
    SOFTWARE_INSTALLED,       // Check if software is installed
    CUSTOM_SCRIPT            // Custom PowerShell condition script
};

struct ExecutionCondition {
    ConditionType type = ConditionType::ALWAYS;
    std::string parameter;     // Parameter for condition (e.g., registry path, file path)
    std::string expectedValue; // Expected value for comparison
    std::string operator_;     // Comparison operator (==, !=, >, <, >=, <=, contains)
    bool negate = false;       // Negate the condition result
};

struct PackageStep {
    PackageStepType type = PackageStepType::UNKNOWN;
    std::string name;
    fs::path targetPath;
    std::string arguments;
    fs::path destinationPath;
    bool continueOnError = false;
    int timeoutSeconds = 300;              // Step timeout in seconds
    int retryCount = 0;                    // Number of retries on failure
    int retryDelaySeconds = 5;             // Delay between retries
    ExecutionCondition condition;          // Execution condition
    std::map<std::string, std::string> parameters; // Additional parameters
    std::string workingDirectory;          // Working directory for execution
    std::string runAsUser;                 // Run as specific user (optional)
    bool requiresReboot = false;           // Step requires reboot after execution
    std::vector<int> successExitCodes = {0}; // Exit codes considered successful
};

struct PackageManifest {
    std::string packageName;
    std::string packageVersion;
    std::string description;
    std::vector<StagedFile> filesToStage;
    std::vector<PackageStep> deploymentSteps;

    static PackageManifest createDefaultManifest(const std::string& name, const std::string& version) {
        PackageManifest manifest;
        manifest.packageName = name;
        manifest.packageVersion = version;
        manifest.description = "A software package.";
        return manifest;
    }

    void addFileToStage(const std::string& srcInPkg, const std::string& destInStaging) {
        filesToStage.push_back({fs::path(srcInPkg), fs::path(destInStaging)});
    }

    void addInstallStep(const std::string& stepName, const std::string& installerPathInStaging, const std::string& args, bool onError = false) {
        deploymentSteps.push_back({
            PackageStepType::INSTALL, stepName, fs::path(installerPathInStaging), args, "", onError
        });
    }

    void addCommandStep(const std::string& stepName, const std::string& commandArguments, bool onError = false) {
        // For COMMAND type, targetPath might be empty if command is a full path itself,
        // or it could be the command and arguments could be separate.
        // For simplicity here, assuming 'commandArguments' is the full thing to run.
        deploymentSteps.push_back({
            PackageStepType::COMMAND, stepName, "", commandArguments, "", onError
        });
    }

    void addPowerShellStep(const std::string& stepName, const std::string& scriptPathInStaging, bool onError = false) {
        deploymentSteps.push_back({
            PackageStepType::POWERSHELL, stepName, fs::path(scriptPathInStaging), "", "", onError
        });
    }

    void addFileCopyStep(const std::string& stepName, const std::string& sourceOnTargetStaging, const std::string& destOnTargetSystem, bool onError = false) {
        deploymentSteps.push_back({
            PackageStepType::FILE_COPY, stepName, fs::path(sourceOnTargetStaging), "", fs::path(destOnTargetSystem), onError
        });
    }

    void addRebootStep(const std::string& stepName, bool onError = false) {
         deploymentSteps.push_back({
            PackageStepType::REBOOT, stepName, "", "", "", onError
        });
    }
};
#endif // PACKAGE_MANIFEST_H
