name: Build
on:
    pull_request:
    push:
        branches: [ ci, master, latest, 3.3-stable ]
    workflow_dispatch:
permissions:
    statuses: write
    contents: read

jobs:
    build-linux-clang:
        name: Linux (Clang)
        runs-on: ubuntu-latest
        timeout-minutes: 4
        env:
            CC: clang
            CFLAGS: -Werror
        steps:
            - uses: actions/checkout@v4
            - name: Install dependencies
              run: |
                  sudo apt update
                  sudo apt install libxrandr-dev libxinerama-dev libxcursor-dev libxi-dev libxext-dev libwayland-dev libxkbcommon-dev

            - name: Configure Null shared library
              run: cmake -B build-null-shared -D GLFW_BUILD_WAYLAND=OFF -D GLFW_BUILD_X11=OFF -D BUILD_SHARED_LIBS=ON
            - name: Build Null shared library
              run: cmake --build build-null-shared --parallel

            - name: Configure X11 shared library
              run: cmake -B build-x11-shared -D GLFW_BUILD_WAYLAND=OFF -D GLFW_BUILD_X11=ON -D BUILD_SHARED_LIBS=ON
            - name: Build X11 shared library
              run: cmake --build build-x11-shared --parallel

            - name: Configure Wayland shared library
              run: cmake -B build-wayland-shared -D GLFW_BUILD_WAYLAND=ON -D GLFW_BUILD_X11=OFF -D BUILD_SHARED_LIBS=ON
            - name: Build Wayland shared library
              run: cmake --build build-wayland-shared --parallel

            - name: Configure Wayland+X11 static library
              run: cmake -B build-full-static -D GLFW_BUILD_WAYLAND=ON -D GLFW_BUILD_X11=ON
            - name: Build Wayland+X11 static library
              run: cmake --build build-full-static --parallel

            - name: Configure Wayland+X11 shared library
              run: cmake -B build-full-shared -D GLFW_BUILD_WAYLAND=ON -D BUILD_SHARED_LIBS=ON -D GLFW_BUILD_X11=ON
            - name: Build Wayland+X11 shared library
              run: cmake --build build-full-shared --parallel

    build-macos-clang:
        name: macOS (Clang)
        runs-on: macos-latest
        timeout-minutes: 4
        env:
            CFLAGS: -Werror
            MACOSX_DEPLOYMENT_TARGET: 10.8
            CMAKE_OSX_ARCHITECTURES: x86_64;arm64
        steps:
            - uses: actions/checkout@v4

            - name: Configure Null shared library
              run: cmake -B build-null-shared -D GLFW_BUILD_COCOA=OFF -D BUILD_SHARED_LIBS=ON
            - name: Build Null shared library
              run: cmake --build build-null-shared --parallel

            - name: Configure Cocoa static library
              run: cmake -B build-cocoa-static
            - name: Build Cocoa static library
              run: cmake --build build-cocoa-static --parallel

            - name: Configure Cocoa shared library
              run: cmake -B build-cocoa-shared -D BUILD_SHARED_LIBS=ON
            - name: Build Cocoa shared library
              run: cmake --build build-cocoa-shared --parallel

    build-windows-vs2022:
        name: Windows (VS2022)
        runs-on: windows-latest
        timeout-minutes: 4
        env:
            CFLAGS: /WX
        steps:
            - uses: actions/checkout@v4

            - name: Configure Win32 shared x86 library
              run: cmake -B build-win32-shared-x86 -G "Visual Studio 17 2022" -A Win32 -D BUILD_SHARED_LIBS=ON
            - name: Build Win32 shared x86 library
              run: cmake --build build-win32-shared-x86 --parallel

            - name: Configure Win32 static x64 library
              run: cmake -B build-win32-static-x64 -G "Visual Studio 17 2022" -A x64
            - name: Build Win32 static x64 library
              run: cmake --build build-win32-static-x64 --parallel

            - name: Configure Win32 shared x64 library
              run: cmake -B build-win32-shared-x64 -G "Visual Studio 17 2022" -A x64 -D BUILD_SHARED_LIBS=ON
            - name: Build Win32 shared x64 library
              run: cmake --build build-win32-shared-x64 --parallel

