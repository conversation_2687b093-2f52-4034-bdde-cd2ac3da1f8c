#ifndef ACTIVE_DIRECTORY_MANAGER_H
#define ACTIVE_DIRECTORY_MANAGER_H

#include "Credentials.h"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <chrono>
#include <filesystem>

namespace fs = std::filesystem;

// Computer information from Active Directory
struct ADComputerInfo {
    std::string name;
    std::string distinguishedName;
    std::string dnsHostName;
    std::string operatingSystem;
    std::string operatingSystemVersion;
    std::string operatingSystemServicePack;
    std::string description;
    std::string location;
    std::string managedBy;
    std::chrono::system_clock::time_point lastLogon;
    std::chrono::system_clock::time_point whenCreated;
    std::chrono::system_clock::time_point whenChanged;
    bool enabled = true;
    std::string ipAddress;
    std::string macAddress;
    std::map<std::string, std::string> customAttributes;
};

// Organizational Unit information
struct ADOrganizationalUnit {
    std::string name;
    std::string distinguishedName;
    std::string description;
    std::vector<std::string> childOUs;
    std::vector<std::string> computers;
    std::vector<std::string> users;
};

// Group information
struct ADGroup {
    std::string name;
    std::string distinguishedName;
    std::string description;
    std::string groupType;
    std::string groupScope;
    std::vector<std::string> members;
    std::vector<std::string> memberOf;
};

// User information
struct ADUser {
    std::string name;
    std::string distinguishedName;
    std::string displayName;
    std::string email;
    std::string department;
    std::string title;
    std::string manager;
    bool enabled = true;
    std::chrono::system_clock::time_point lastLogon;
    std::vector<std::string> memberOf;
};

// LDAP query filter builder
class LDAPFilter {
public:
    LDAPFilter();
    
    // Basic filters
    LDAPFilter& equals(const std::string& attribute, const std::string& value);
    LDAPFilter& contains(const std::string& attribute, const std::string& value);
    LDAPFilter& startsWith(const std::string& attribute, const std::string& value);
    LDAPFilter& endsWith(const std::string& attribute, const std::string& value);
    LDAPFilter& exists(const std::string& attribute);
    LDAPFilter& notExists(const std::string& attribute);
    
    // Logical operators
    LDAPFilter& and_(const LDAPFilter& other);
    LDAPFilter& or_(const LDAPFilter& other);
    LDAPFilter& not_();
    
    // Build final filter string
    std::string build() const;
    
    // Predefined filters
    static LDAPFilter computers();
    static LDAPFilter enabledComputers();
    static LDAPFilter servers();
    static LDAPFilter workstations();
    static LDAPFilter users();
    static LDAPFilter enabledUsers();
    static LDAPFilter groups();

private:
    std::string filter_;
};

// Active Directory connection and query manager
class ActiveDirectoryManager {
public:
    ActiveDirectoryManager();
    ~ActiveDirectoryManager();
    
    // Initialize connection to Active Directory
    bool initialize(const std::string& domain = "", const DeployCredentials& credentials = {});
    
    // Test connection to Active Directory
    bool testConnection();
    
    // Get domain information
    std::string getDomainName();
    std::vector<std::string> getDomainControllers();
    std::string getDefaultNamingContext();
    
    // Computer queries
    std::vector<ADComputerInfo> getAllComputers();
    std::vector<ADComputerInfo> getComputersInOU(const std::string& ouDN);
    std::vector<ADComputerInfo> getComputersByFilter(const LDAPFilter& filter);
    std::unique_ptr<ADComputerInfo> getComputerByName(const std::string& computerName);
    std::vector<ADComputerInfo> searchComputers(const std::string& searchTerm);
    
    // Organizational Unit queries
    std::vector<ADOrganizationalUnit> getAllOUs();
    std::vector<ADOrganizationalUnit> getChildOUs(const std::string& parentOUDN);
    std::unique_ptr<ADOrganizationalUnit> getOUByDN(const std::string& ouDN);
    std::vector<ADOrganizationalUnit> searchOUs(const std::string& searchTerm);
    
    // Group queries
    std::vector<ADGroup> getAllGroups();
    std::vector<ADGroup> getGroupsInOU(const std::string& ouDN);
    std::unique_ptr<ADGroup> getGroupByName(const std::string& groupName);
    std::vector<ADGroup> getGroupsForComputer(const std::string& computerName);
    std::vector<ADGroup> searchGroups(const std::string& searchTerm);
    
    // User queries
    std::vector<ADUser> getAllUsers();
    std::vector<ADUser> getUsersInOU(const std::string& ouDN);
    std::unique_ptr<ADUser> getUserByName(const std::string& userName);
    std::vector<ADUser> searchUsers(const std::string& searchTerm);
    
    // LAPS (Local Administrator Password Solution) support
    bool isLAPSEnabled();
    std::string getLAPSPassword(const std::string& computerName);
    std::chrono::system_clock::time_point getLAPSPasswordExpiration(const std::string& computerName);
    bool setLAPSPassword(const std::string& computerName, const std::string& password);
    
    // Computer management
    bool enableComputer(const std::string& computerName);
    bool disableComputer(const std::string& computerName);
    bool moveComputer(const std::string& computerName, const std::string& targetOUDN);
    bool updateComputerDescription(const std::string& computerName, const std::string& description);
    
    // Group membership management
    bool addComputerToGroup(const std::string& computerName, const std::string& groupName);
    bool removeComputerFromGroup(const std::string& computerName, const std::string& groupName);
    std::vector<std::string> getComputerGroups(const std::string& computerName);
    
    // Caching and synchronization
    void enableCaching(bool enabled, int cacheTimeoutMinutes = 60);
    bool refreshCache();
    void clearCache();
    std::chrono::system_clock::time_point getLastSyncTime();
    
    // Export/Import functionality
    bool exportComputersToCSV(const fs::path& filePath, const std::vector<ADComputerInfo>& computers = {});
    bool exportOUsToCSV(const fs::path& filePath);
    std::vector<std::string> importComputersFromCSV(const fs::path& filePath);
    
    // Statistics and monitoring
    int getTotalComputerCount();
    int getEnabledComputerCount();
    int getDisabledComputerCount();
    std::map<std::string, int> getComputersByOS();
    std::map<std::string, int> getComputersByOU();
    
    // Advanced queries
    std::vector<ADComputerInfo> getComputersNotSeenSince(const std::chrono::system_clock::time_point& since);
    std::vector<ADComputerInfo> getComputersCreatedSince(const std::chrono::system_clock::time_point& since);
    std::vector<ADComputerInfo> getComputersByOperatingSystem(const std::string& osPattern);
    
    // Error handling
    std::string getLastError() const;
    bool hasError() const;
    void clearError();

private:
    // LDAP connection management
    bool connectToLDAP();
    void disconnectFromLDAP();
    bool bindToLDAP();
    
    // Query execution
    std::vector<std::map<std::string, std::string>> executeQuery(const std::string& baseDN, 
                                                                const std::string& filter,
                                                                const std::vector<std::string>& attributes);
    
    // Data conversion helpers
    ADComputerInfo mapToComputerInfo(const std::map<std::string, std::string>& attributes);
    ADOrganizationalUnit mapToOU(const std::map<std::string, std::string>& attributes);
    ADGroup mapToGroup(const std::map<std::string, std::string>& attributes);
    ADUser mapToUser(const std::map<std::string, std::string>& attributes);
    
    // Time conversion helpers
    std::chrono::system_clock::time_point parseADTimestamp(const std::string& timestamp);
    std::string formatADTimestamp(const std::chrono::system_clock::time_point& time);
    
    // Cache management
    void updateCache(const std::string& key, const std::string& data);
    std::string getFromCache(const std::string& key);
    bool isCacheValid(const std::string& key);
    
    // Member variables
    std::string domain_;
    DeployCredentials credentials_;
    void* ldapConnection_;  // Platform-specific LDAP handle
    bool isConnected_;
    bool isInitialized_;
    
    // Caching
    bool cachingEnabled_;
    int cacheTimeoutMinutes_;
    std::map<std::string, std::pair<std::string, std::chrono::system_clock::time_point>> cache_;
    std::chrono::system_clock::time_point lastSyncTime_;
    
    // Error handling
    std::string lastError_;
    
    // Thread safety
    mutable std::mutex adMutex_;
};

// Utility functions for Active Directory operations
namespace ADUtils {
    // Parse distinguished name components
    std::map<std::string, std::string> parseDN(const std::string& dn);
    
    // Build distinguished name from components
    std::string buildDN(const std::map<std::string, std::string>& components);
    
    // Extract computer name from DN
    std::string getComputerNameFromDN(const std::string& dn);
    
    // Extract OU name from DN
    std::string getOUNameFromDN(const std::string& dn);
    
    // Validate computer name format
    bool isValidComputerName(const std::string& name);
    
    // Escape LDAP filter special characters
    std::string escapeLDAPFilter(const std::string& input);
    
    // Convert Windows timestamp to system time
    std::chrono::system_clock::time_point windowsTimeToSystemTime(uint64_t windowsTime);
    
    // Convert system time to Windows timestamp
    uint64_t systemTimeToWindowsTime(const std::chrono::system_clock::time_point& time);
    
    // Get current domain from environment
    std::string getCurrentDomain();
    
    // Check if running in domain environment
    bool isInDomainEnvironment();
}

#endif // ACTIVE_DIRECTORY_MANAGER_H
