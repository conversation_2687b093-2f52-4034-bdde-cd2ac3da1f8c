# XDeploy - Agentless PDQ Deploy Clone

A comprehensive, cross-platform deployment tool inspired by PDQ Deploy, designed for agentless software deployment across Windows networks.

## Overview

XDeploy is a powerful, enterprise-grade deployment solution that replicates the core functionality of PDQ Deploy while adding modern enhancements. It provides an agentless approach to software deployment, using temporary runner services and robust communication protocols to deploy packages across your network infrastructure.

## Key Features

### 🚀 Agentless Deployment
- **No permanent agents required** - Deploys temporary runner services only when needed
- **Multiple deployment modes** - Push, Pull, and Hybrid deployment strategies
- **Automatic cleanup** - Removes all deployment artifacts after completion
- **Real-time monitoring** - Live progress tracking and status updates

### 🔐 Advanced Security
- **Encrypted credential storage** - Uses Windows DPAPI or equivalent for secure credential management
- **LAPS integration** - Full support for Microsoft Local Administrator Password Solution
- **Role-based access** - Granular permissions and audit trails
- **Secure communication** - Encrypted channels for all remote operations

### 📦 Flexible Package Management
- **Multiple step types** - Install, Command, PowerShell, Batch, File Copy, Registry, Service operations
- **Conditional execution** - Smart deployment based on system conditions
- **Retry mechanisms** - Automatic retry with configurable delays
- **Package versioning** - Track and manage package versions

### 🌐 Network Discovery & Targeting
- **Active Directory integration** - Browse and sync with AD computers and OUs
- **Network scanning** - Discover online hosts across subnets
- **Dynamic targeting** - Create target groups based on criteria
- **Inventory integration** - Ready for future inventory system integration

### 📊 Comprehensive Reporting
- **Multiple report types** - Deployment summaries, package usage, error analysis
- **Various formats** - HTML, PDF, CSV, JSON, XML, Excel
- **Scheduled reports** - Automatic report generation and distribution
- **Performance metrics** - Detailed analytics and trends

### 🖥️ Modern GUI Interface
- **PDQ Deploy-style interface** - Familiar layout for easy migration
- **Real-time updates** - Live deployment progress and status
- **Multi-panel design** - Packages, Targets, Deployments, and Logs
- **Customizable views** - Flexible interface configuration

## Architecture

XDeploy follows a client-server architecture with the following components:

### Core Components

1. **Main Console Application** (`xdeploy.exe`)
   - Central management interface
   - Package creation and management
   - Target discovery and selection
   - Deployment orchestration
   - Real-time monitoring and reporting

2. **Temporary Runner Service** (`xdeploy_runner.exe`)
   - Lightweight executable deployed to target machines
   - Executes deployment steps locally on targets
   - Reports status back to console
   - Automatically removes itself after completion

3. **Communication Layer**
   - Uses SMB for file transfer
   - PowerShell Remoting for command execution
   - WMI for system information gathering
   - Secure credential handling

### Key Classes and Modules

- **DeploymentEngine** - Core deployment orchestration
- **CredentialManager** - Secure credential storage and LAPS integration
- **PackageManifest** - Package definition and step management
- **NetworkDiscovery** - Network scanning and host discovery
- **ActiveDirectoryManager** - AD integration and computer management
- **ReportingManager** - Report generation and analytics
- **EnhancedGuiManager** - Modern user interface

## Installation

### Prerequisites

- Windows 10/11 or Windows Server 2016+
- .NET Framework 4.8 or later
- Administrative privileges on deployment console
- Network access to target machines
- PowerShell 5.1 or later

### Build Requirements

- Visual Studio 2019 or later
- CMake 3.10 or later
- C++17 compatible compiler
- Windows SDK

### Building from Source

```bash
# Clone the repository
git clone https://github.com/your-org/xdeploy.git
cd xdeploy

# Create build directory
mkdir build
cd build

# Configure with CMake
cmake ../xdeploy -G "Visual Studio 16 2019" -A x64

# Build the project
cmake --build . --config Release

# The executable will be in build/Release/xdeploy.exe
```

## Quick Start

### 1. Initial Setup

1. Launch XDeploy as Administrator
2. Configure credentials in **Edit > Credentials**
3. Set up default deployment settings in **Edit > Preferences**

### 2. Create Your First Package

1. Click **Package > Create Package** or use the toolbar button
2. Define package properties (name, version, description)
3. Add deployment steps:
   - **Install Step**: MSI/EXE installers with silent parameters
   - **PowerShell Step**: Custom PowerShell scripts
   - **File Copy Step**: Copy files to specific locations
   - **Registry Step**: Registry modifications
   - **Service Step**: Service management operations

### 3. Discover Target Machines

1. Go to the **Targets** panel
2. Click **Scan Network** to discover machines
3. Or use **Tools > Active Directory** to browse AD computers
4. Select target machines for deployment

### 4. Deploy Package

1. Select package from **Packages** panel
2. Select target machines from **Targets** panel
3. Click **Deploy Now** or schedule for later
4. Monitor progress in **Deployments** panel

## Configuration

### Credential Management

XDeploy supports multiple credential types:

- **Local Administrator** - Local machine credentials
- **Domain Administrator** - Domain-wide administrative access
- **LAPS Credentials** - Automatic retrieval from LAPS
- **Service Accounts** - Dedicated deployment service accounts

### Package Step Types

#### Install Step
```yaml
type: INSTALL
name: "Install Application"
targetPath: "installers/app.msi"
arguments: "/qn ALLUSERS=1 /norestart"
continueOnError: false
timeoutSeconds: 600
```

#### PowerShell Step
```yaml
type: POWERSHELL
name: "Configure Application"
targetPath: "scripts/configure.ps1"
arguments: "-Parameter Value"
runAsUser: "SYSTEM"
timeoutSeconds: 300
```

#### File Copy Step
```yaml
type: FILE_COPY
name: "Copy Configuration"
targetPath: "config/app.config"
destinationPath: "C:/ProgramData/App/app.config"
continueOnError: true
```

#### Registry Step
```yaml
type: REGISTRY
name: "Set Registry Values"
parameters:
  action: "set"
  hive: "HKLM"
  key: "SOFTWARE\\MyApp"
  value: "Version"
  data: "1.0.0"
  type: "REG_SZ"
```

### Conditional Execution

Steps can be executed conditionally based on system state:

```yaml
condition:
  type: OS_VERSION
  parameter: "Windows 10"
  operator: ">="
  expectedValue: "10.0.19041"
```

## Command Line Interface

XDeploy also provides a comprehensive CLI for automation:

```bash
# Set credentials
xdeploy --cli set-creds username password domain

# Discover hosts
xdeploy --cli discover *************

# Create package
xdeploy --cli package ./source-files ./output-package

# Deploy package
xdeploy --cli deploy ./package-dir target-host

# Execute remote command
xdeploy --cli rexec target-host "command to execute"

# Run remote script
xdeploy --cli run-script target-host script.ps1 PS1
```

## Advanced Features

### Active Directory Integration

- **Computer Discovery**: Browse AD computers and OUs
- **Group Targeting**: Deploy to AD computer groups
- **LAPS Support**: Automatic password retrieval
- **Synchronization**: Keep target lists up-to-date with AD

### Deployment Scheduling

- **One-time Deployments**: Schedule for specific date/time
- **Recurring Deployments**: Daily, weekly, monthly schedules
- **Heartbeat Deployments**: Deploy when machines come online
- **Maintenance Windows**: Respect organizational maintenance schedules

### Reporting and Analytics

- **Deployment Reports**: Success rates, timing, errors
- **Package Usage**: Track which packages are deployed where
- **Target Status**: Machine health and deployment history
- **Performance Metrics**: Identify bottlenecks and trends
- **Error Analysis**: Common failures and resolutions

### Security Features

- **Encrypted Storage**: All credentials encrypted at rest
- **Audit Logging**: Complete audit trail of all actions
- **Role-Based Access**: Granular permission system
- **Secure Communication**: Encrypted network protocols
- **LAPS Integration**: Seamless password management

## Troubleshooting

### Common Issues

#### Connection Problems
```
Error: Cannot connect to target machine
Solution:
1. Verify target is online and accessible
2. Check firewall settings (WMI, PowerShell Remoting)
3. Ensure credentials have administrative privileges
4. Test with: xdeploy --cli discover <target-ip>
```

#### Deployment Failures
```
Error: Package deployment failed
Solution:
1. Check deployment logs in Logs panel
2. Verify package integrity and step configuration
3. Test individual steps manually
4. Check target system requirements
```

#### Permission Issues
```
Error: Access denied during deployment
Solution:
1. Verify credentials have local admin rights
2. Check UAC settings on target machines
3. Ensure PowerShell execution policy allows scripts
4. Consider using LAPS for password management
```

### Log Files

- **Application Logs**: `%APPDATA%/XDeploy/logs/`
- **Deployment Logs**: `%TEMP%/xdeploy_deployments/`
- **Configuration**: `%APPDATA%/XDeploy/config/`

## Comparison with PDQ Deploy

| Feature | XDeploy | PDQ Deploy |
|---------|---------|------------|
| Agentless Deployment | ✅ | ✅ |
| Package Management | ✅ | ✅ |
| Active Directory Integration | ✅ | ✅ |
| LAPS Support | ✅ | ✅ |
| Scheduling | ✅ | ✅ |
| Reporting | ✅ | ✅ |
| Cross-Platform | ✅ | ❌ |
| Open Source | ✅ | ❌ |
| Custom Step Types | ✅ | Limited |
| REST API | 🚧 | ❌ |
| Cloud Integration | 🚧 | ❌ |

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

### Code Style

- Follow C++17 standards
- Use meaningful variable names
- Add comments for complex logic
- Include unit tests for new features

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Documentation**: [Wiki](https://github.com/your-org/xdeploy/wiki)
- **Issues**: [GitHub Issues](https://github.com/your-org/xdeploy/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/xdeploy/discussions)
- **Email**: <EMAIL>

## Roadmap

### Version 2.0 (Planned)
- [ ] REST API for automation
- [ ] Web-based management interface
- [ ] Cloud deployment support
- [ ] Linux/macOS target support
- [ ] Container deployment
- [ ] Integration with popular CI/CD tools

### Version 1.5 (In Progress)
- [x] Enhanced GUI interface
- [x] Advanced reporting system
- [x] LAPS integration
- [ ] PowerShell DSC support
- [ ] Chocolatey package integration

## Acknowledgments

- Inspired by PDQ Deploy from PDQ.com
- Built with modern C++17 and Windows APIs
- Uses ImGui for cross-platform GUI
- Leverages PowerShell for remote operations

---

**XDeploy** - Making software deployment simple, secure, and scalable.