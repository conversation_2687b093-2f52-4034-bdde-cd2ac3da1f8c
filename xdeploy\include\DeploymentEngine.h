#ifndef DEPLOYMENT_ENGINE_H
#define DEPLOYMENT_ENGINE_H

#include "PackageManifest.h"
#include "Credentials.h"
#include "CredentialManager.h"
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include <chrono>
#include <thread>
#include <mutex>
#include <atomic>
#include <filesystem>

namespace fs = std::filesystem;

// Deployment status enumeration
enum class DeploymentStatus {
    PENDING,        // Deployment is queued
    PREPARING,      // Preparing deployment (copying files, etc.)
    RUNNING,        // Deployment is actively running
    SUCCESS,        // Deployment completed successfully
    FAILED,         // Deployment failed
    CANCELLED,      // Deployment was cancelled
    TIMEOUT,        // Deployment timed out
    RETRY,          // Deployment is being retried
    PARTIAL_SUCCESS // Some steps succeeded, others failed
};

// Step execution status
enum class StepStatus {
    PENDING,
    RUNNING,
    SUCCESS,
    FAILED,
    SKIPPED,
    TIMEOUT,
    RETRY
};

// Deployment mode enumeration
enum class DeploymentMode {
    PUSH,           // Push files to target, then execute
    PULL,           // Target pulls files from share, then executes
    HYBRID          // Combination of push and pull
};

// Real-time deployment progress information
struct DeploymentProgress {
    std::string deploymentId;
    std::string targetHost;
    std::string packageName;
    DeploymentStatus status;
    int currentStep = 0;
    int totalSteps = 0;
    std::string currentStepName;
    StepStatus currentStepStatus;
    int progressPercentage = 0;
    std::chrono::system_clock::time_point startTime;
    std::chrono::system_clock::time_point lastUpdateTime;
    std::string errorMessage;
    std::map<std::string, std::string> metadata;
};

// Step execution result
struct StepResult {
    std::string stepName;
    StepStatus status;
    int exitCode = 0;
    std::string output;
    std::string errorOutput;
    std::chrono::system_clock::time_point startTime;
    std::chrono::system_clock::time_point endTime;
    int retryAttempt = 0;
};

// Deployment result summary
struct DeploymentResult {
    std::string deploymentId;
    std::string targetHost;
    std::string packageName;
    DeploymentStatus finalStatus;
    std::chrono::system_clock::time_point startTime;
    std::chrono::system_clock::time_point endTime;
    std::vector<StepResult> stepResults;
    std::string errorSummary;
    std::map<std::string, std::string> metadata;
};

// Callback function types for progress reporting
using ProgressCallback = std::function<void(const DeploymentProgress&)>;
using CompletionCallback = std::function<void(const DeploymentResult&)>;

// Deployment configuration
struct DeploymentConfig {
    DeploymentMode mode = DeploymentMode::PUSH;
    int globalTimeoutMinutes = 60;
    int stepTimeoutMinutes = 10;
    int maxRetries = 3;
    int retryDelaySeconds = 30;
    bool continueOnError = false;
    bool cleanupOnFailure = true;
    bool cleanupOnSuccess = true;
    std::string workingDirectory = "C:\\Windows\\Temp\\xdeploy";
    std::string logLevel = "INFO"; // DEBUG, INFO, WARN, ERROR
    bool enableRealTimeLogging = true;
    std::map<std::string, std::string> customParameters;
};

// Forward declarations
class RemoteSystem;
class CredentialManager;

// Main deployment engine class
class DeploymentEngine {
public:
    DeploymentEngine();
    ~DeploymentEngine();
    
    // Initialize the deployment engine
    bool initialize(std::shared_ptr<CredentialManager> credentialManager);
    
    // Set deployment configuration
    void setConfiguration(const DeploymentConfig& config);
    
    // Get current configuration
    const DeploymentConfig& getConfiguration() const;
    
    // Deploy package to single target
    std::string deployPackage(const fs::path& packagePath, const std::string& targetHost,
                             const std::string& credentialId = "",
                             ProgressCallback progressCallback = nullptr,
                             CompletionCallback completionCallback = nullptr);
    
    // Deploy package to multiple targets
    std::vector<std::string> deployPackageToMultipleTargets(
        const fs::path& packagePath,
        const std::vector<std::string>& targetHosts,
        const std::string& credentialId = "",
        ProgressCallback progressCallback = nullptr,
        CompletionCallback completionCallback = nullptr);
    
    // Cancel deployment
    bool cancelDeployment(const std::string& deploymentId);
    
    // Get deployment progress
    std::unique_ptr<DeploymentProgress> getDeploymentProgress(const std::string& deploymentId);
    
    // Get deployment result
    std::unique_ptr<DeploymentResult> getDeploymentResult(const std::string& deploymentId);
    
    // Get all active deployments
    std::vector<std::string> getActiveDeployments();
    
    // Get deployment history
    std::vector<DeploymentResult> getDeploymentHistory(int maxResults = 100);
    
    // Test connectivity to target host
    bool testConnectivity(const std::string& targetHost, const std::string& credentialId = "");
    
    // Get system information from target host
    std::map<std::string, std::string> getSystemInfo(const std::string& targetHost, 
                                                     const std::string& credentialId = "");
    
    // Cleanup deployment artifacts on target
    bool cleanupTarget(const std::string& targetHost, const std::string& deploymentId,
                      const std::string& credentialId = "");
    
    // Set global progress callback for all deployments
    void setGlobalProgressCallback(ProgressCallback callback);
    
    // Set global completion callback for all deployments
    void setGlobalCompletionCallback(CompletionCallback callback);

private:
    // Internal deployment worker thread
    void deploymentWorker(const std::string& deploymentId);
    
    // Execute single deployment step
    StepResult executeStep(const PackageStep& step, const std::string& targetHost,
                          const std::string& workingDir, std::shared_ptr<RemoteSystem> remoteSystem);
    
    // Evaluate step condition
    bool evaluateCondition(const ExecutionCondition& condition, const std::string& targetHost,
                          std::shared_ptr<RemoteSystem> remoteSystem);
    
    // Setup remote environment for deployment
    bool setupRemoteEnvironment(const std::string& targetHost, const std::string& deploymentId,
                               const fs::path& packagePath, std::shared_ptr<RemoteSystem> remoteSystem);
    
    // Cleanup remote environment after deployment
    bool cleanupRemoteEnvironment(const std::string& targetHost, const std::string& deploymentId,
                                 std::shared_ptr<RemoteSystem> remoteSystem);
    
    // Generate unique deployment ID
    std::string generateDeploymentId();
    
    // Update deployment progress
    void updateProgress(const std::string& deploymentId, const DeploymentProgress& progress);
    
    // Log deployment event
    void logEvent(const std::string& deploymentId, const std::string& level, 
                 const std::string& message);
    
    // Save deployment result
    void saveDeploymentResult(const DeploymentResult& result);
    
    // Load deployment history from storage
    void loadDeploymentHistory();
    
    DeploymentConfig config_;
    std::shared_ptr<CredentialManager> credentialManager_;
    
    // Thread management
    std::map<std::string, std::unique_ptr<std::thread>> deploymentThreads_;
    std::map<std::string, std::atomic<bool>> cancellationFlags_;
    
    // Progress tracking
    std::map<std::string, DeploymentProgress> activeDeployments_;
    std::map<std::string, DeploymentResult> completedDeployments_;
    std::vector<DeploymentResult> deploymentHistory_;
    
    // Callbacks
    ProgressCallback globalProgressCallback_;
    CompletionCallback globalCompletionCallback_;
    
    // Synchronization
    mutable std::mutex deploymentsMutex_;
    mutable std::mutex historyMutex_;
    
    bool isInitialized_;
};

// Deployment scheduler for timed and recurring deployments
class DeploymentScheduler {
public:
    DeploymentScheduler(std::shared_ptr<DeploymentEngine> engine);
    ~DeploymentScheduler();
    
    // Schedule one-time deployment
    std::string scheduleDeployment(const fs::path& packagePath, 
                                  const std::vector<std::string>& targetHosts,
                                  const std::chrono::system_clock::time_point& scheduledTime,
                                  const std::string& credentialId = "");
    
    // Schedule recurring deployment
    std::string scheduleRecurringDeployment(const fs::path& packagePath,
                                           const std::vector<std::string>& targetHosts,
                                           const std::chrono::minutes& interval,
                                           const std::string& credentialId = "");
    
    // Cancel scheduled deployment
    bool cancelScheduledDeployment(const std::string& scheduleId);
    
    // Get scheduled deployments
    std::vector<std::string> getScheduledDeployments();

private:
    void schedulerWorker();
    
    std::shared_ptr<DeploymentEngine> engine_;
    std::unique_ptr<std::thread> schedulerThread_;
    std::atomic<bool> stopScheduler_;
    std::mutex scheduleMutex_;
};

#endif // DEPLOYMENT_ENGINE_H
