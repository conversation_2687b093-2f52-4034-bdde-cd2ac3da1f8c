﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{853DDB76-395E-36CF-BEFA-70A12910219D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "GLFW3", "GLFW3", "{6BF0572A-E712-31BC-A601-D13AFF30C0E7}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F}"
	ProjectSection(ProjectDependencies) = postProject
		{C01076FF-0F1F-31C6-8371-36F28498E0D5} = {C01076FF-0F1F-31C6-8371-36F28498E0D5}
		{5F30D296-5C51-39BD-BE92-0044D4C3D4C8} = {5F30D296-5C51-39BD-BE92-0044D4C3D4C8}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{00F6331C-5EEC-32A3-905E-444C7DF2A952}"
	ProjectSection(ProjectDependencies) = postProject
		{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F} = {CC2A7A3E-FD84-34E2-B8F3-632E48BD182F}
		{C01076FF-0F1F-31C6-8371-36F28498E0D5} = {C01076FF-0F1F-31C6-8371-36F28498E0D5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{E2D99AC6-B1D5-3A96-9885-6B5B3E957F4C}"
	ProjectSection(ProjectDependencies) = postProject
		{C01076FF-0F1F-31C6-8371-36F28498E0D5} = {C01076FF-0F1F-31C6-8371-36F28498E0D5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{C01076FF-0F1F-31C6-8371-36F28498E0D5}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "glfw", "src\glfw.vcxproj", "{5F30D296-5C51-39BD-BE92-0044D4C3D4C8}"
	ProjectSection(ProjectDependencies) = postProject
		{C01076FF-0F1F-31C6-8371-36F28498E0D5} = {C01076FF-0F1F-31C6-8371-36F28498E0D5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "uninstall", "uninstall.vcxproj", "{168E69C7-F322-34F3-9EDF-4CCEDFE30280}"
	ProjectSection(ProjectDependencies) = postProject
		{C01076FF-0F1F-31C6-8371-36F28498E0D5} = {C01076FF-0F1F-31C6-8371-36F28498E0D5}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "update_mappings", "src\update_mappings.vcxproj", "{BB84DE30-B723-30D3-AE8F-11DDA248901D}"
	ProjectSection(ProjectDependencies) = postProject
		{C01076FF-0F1F-31C6-8371-36F28498E0D5} = {C01076FF-0F1F-31C6-8371-36F28498E0D5}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F}.Debug|x64.ActiveCfg = Debug|x64
		{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F}.Debug|x64.Build.0 = Debug|x64
		{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F}.Release|x64.ActiveCfg = Release|x64
		{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F}.Release|x64.Build.0 = Release|x64
		{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{00F6331C-5EEC-32A3-905E-444C7DF2A952}.Debug|x64.ActiveCfg = Debug|x64
		{00F6331C-5EEC-32A3-905E-444C7DF2A952}.Release|x64.ActiveCfg = Release|x64
		{00F6331C-5EEC-32A3-905E-444C7DF2A952}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{00F6331C-5EEC-32A3-905E-444C7DF2A952}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{E2D99AC6-B1D5-3A96-9885-6B5B3E957F4C}.Debug|x64.ActiveCfg = Debug|x64
		{E2D99AC6-B1D5-3A96-9885-6B5B3E957F4C}.Release|x64.ActiveCfg = Release|x64
		{E2D99AC6-B1D5-3A96-9885-6B5B3E957F4C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{E2D99AC6-B1D5-3A96-9885-6B5B3E957F4C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C01076FF-0F1F-31C6-8371-36F28498E0D5}.Debug|x64.ActiveCfg = Debug|x64
		{C01076FF-0F1F-31C6-8371-36F28498E0D5}.Debug|x64.Build.0 = Debug|x64
		{C01076FF-0F1F-31C6-8371-36F28498E0D5}.Release|x64.ActiveCfg = Release|x64
		{C01076FF-0F1F-31C6-8371-36F28498E0D5}.Release|x64.Build.0 = Release|x64
		{C01076FF-0F1F-31C6-8371-36F28498E0D5}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C01076FF-0F1F-31C6-8371-36F28498E0D5}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C01076FF-0F1F-31C6-8371-36F28498E0D5}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C01076FF-0F1F-31C6-8371-36F28498E0D5}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{5F30D296-5C51-39BD-BE92-0044D4C3D4C8}.Debug|x64.ActiveCfg = Debug|x64
		{5F30D296-5C51-39BD-BE92-0044D4C3D4C8}.Debug|x64.Build.0 = Debug|x64
		{5F30D296-5C51-39BD-BE92-0044D4C3D4C8}.Release|x64.ActiveCfg = Release|x64
		{5F30D296-5C51-39BD-BE92-0044D4C3D4C8}.Release|x64.Build.0 = Release|x64
		{5F30D296-5C51-39BD-BE92-0044D4C3D4C8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{5F30D296-5C51-39BD-BE92-0044D4C3D4C8}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{5F30D296-5C51-39BD-BE92-0044D4C3D4C8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{5F30D296-5C51-39BD-BE92-0044D4C3D4C8}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{168E69C7-F322-34F3-9EDF-4CCEDFE30280}.Debug|x64.ActiveCfg = Debug|x64
		{168E69C7-F322-34F3-9EDF-4CCEDFE30280}.Release|x64.ActiveCfg = Release|x64
		{168E69C7-F322-34F3-9EDF-4CCEDFE30280}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{168E69C7-F322-34F3-9EDF-4CCEDFE30280}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{BB84DE30-B723-30D3-AE8F-11DDA248901D}.Debug|x64.ActiveCfg = Debug|x64
		{BB84DE30-B723-30D3-AE8F-11DDA248901D}.Release|x64.ActiveCfg = Release|x64
		{BB84DE30-B723-30D3-AE8F-11DDA248901D}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{BB84DE30-B723-30D3-AE8F-11DDA248901D}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{CC2A7A3E-FD84-34E2-B8F3-632E48BD182F} = {853DDB76-395E-36CF-BEFA-70A12910219D}
		{00F6331C-5EEC-32A3-905E-444C7DF2A952} = {853DDB76-395E-36CF-BEFA-70A12910219D}
		{E2D99AC6-B1D5-3A96-9885-6B5B3E957F4C} = {853DDB76-395E-36CF-BEFA-70A12910219D}
		{C01076FF-0F1F-31C6-8371-36F28498E0D5} = {853DDB76-395E-36CF-BEFA-70A12910219D}
		{5F30D296-5C51-39BD-BE92-0044D4C3D4C8} = {6BF0572A-E712-31BC-A601-D13AFF30C0E7}
		{168E69C7-F322-34F3-9EDF-4CCEDFE30280} = {6BF0572A-E712-31BC-A601-D13AFF30C0E7}
		{BB84DE30-B723-30D3-AE8F-11DDA248901D} = {6BF0572A-E712-31BC-A601-D13AFF30C0E7}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B7152A24-21F2-30E3-A9F2-56C90AB93DCA}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
