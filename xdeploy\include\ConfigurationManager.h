#ifndef CONFIGURATION_MANAGER_H
#define CONFIGURATION_MANAGER_H

#include <string>
#include <map>
#include <vector>
#include <filesystem>
#include <memory>
#include <mutex>

namespace fs = std::filesystem;

// Configuration categories
enum class ConfigCategory {
    GENERAL,
    DEPLOYMENT,
    NETWORK,
    SECURITY,
    GUI,
    LOGGING,
    ADVANCED
};

// Configuration value types
enum class ConfigValueType {
    STRING,
    INTEGER,
    BOOLEAN,
    DOUBLE,
    PATH,
    LIST
};

// Configuration item definition
struct ConfigItem {
    std::string key;
    std::string displayName;
    std::string description;
    ConfigValueType type;
    ConfigCategory category;
    std::string defaultValue;
    std::string currentValue;
    std::vector<std::string> allowedValues; // For enumerated values
    std::string validationRegex;            // For string validation
    int minValue = 0;                       // For numeric values
    int maxValue = 0;                       // For numeric values
    bool isRequired = false;
    bool isReadOnly = false;
    bool requiresRestart = false;
};

// Configuration manager class
class ConfigurationManager {
public:
    ConfigurationManager();
    ~ConfigurationManager();
    
    // Initialize configuration manager
    bool initialize(const fs::path& configFilePath = "");
    
    // Load configuration from file
    bool loadConfiguration();
    
    // Save configuration to file
    bool saveConfiguration();
    
    // Reset to default values
    void resetToDefaults();
    
    // Get configuration value
    std::string getString(const std::string& key, const std::string& defaultValue = "");
    int getInt(const std::string& key, int defaultValue = 0);
    bool getBool(const std::string& key, bool defaultValue = false);
    double getDouble(const std::string& key, double defaultValue = 0.0);
    fs::path getPath(const std::string& key, const fs::path& defaultValue = "");
    std::vector<std::string> getList(const std::string& key, const std::vector<std::string>& defaultValue = {});
    
    // Set configuration value
    bool setString(const std::string& key, const std::string& value);
    bool setInt(const std::string& key, int value);
    bool setBool(const std::string& key, bool value);
    bool setDouble(const std::string& key, double value);
    bool setPath(const std::string& key, const fs::path& value);
    bool setList(const std::string& key, const std::vector<std::string>& value);
    
    // Get all configuration items
    std::vector<ConfigItem> getAllConfigItems();
    
    // Get configuration items by category
    std::vector<ConfigItem> getConfigItemsByCategory(ConfigCategory category);
    
    // Get specific configuration item
    std::unique_ptr<ConfigItem> getConfigItem(const std::string& key);
    
    // Validate configuration value
    bool validateValue(const std::string& key, const std::string& value);
    
    // Check if configuration has been modified
    bool isModified() const;
    
    // Get configuration file path
    fs::path getConfigFilePath() const;
    
    // Import configuration from file
    bool importConfiguration(const fs::path& filePath);
    
    // Export configuration to file
    bool exportConfiguration(const fs::path& filePath);
    
    // Register configuration change callback
    void registerChangeCallback(const std::string& key, std::function<void(const std::string&, const std::string&)> callback);
    
    // Remove configuration change callback
    void removeChangeCallback(const std::string& key);

private:
    // Initialize default configuration items
    void initializeDefaults();
    
    // Add configuration item
    void addConfigItem(const std::string& key, const std::string& displayName,
                      const std::string& description, ConfigValueType type,
                      ConfigCategory category, const std::string& defaultValue,
                      bool isRequired = false, bool isReadOnly = false,
                      bool requiresRestart = false);
    
    // Parse configuration file
    bool parseConfigFile(const fs::path& filePath);
    
    // Write configuration file
    bool writeConfigFile(const fs::path& filePath);
    
    // Validate configuration item
    bool validateConfigItem(const ConfigItem& item, const std::string& value);
    
    // Notify change callbacks
    void notifyChangeCallbacks(const std::string& key, const std::string& oldValue, const std::string& newValue);
    
    fs::path configFilePath_;
    std::map<std::string, ConfigItem> configItems_;
    std::map<std::string, std::string> configValues_;
    std::map<std::string, std::function<void(const std::string&, const std::string&)>> changeCallbacks_;
    
    bool isInitialized_;
    bool isModified_;
    mutable std::mutex configMutex_;
};

// Global configuration instance
extern std::unique_ptr<ConfigurationManager> g_configManager;

// Configuration utility functions
namespace ConfigUtils {
    // Get application data directory
    fs::path getAppDataDirectory();
    
    // Get application configuration directory
    fs::path getConfigDirectory();
    
    // Get application log directory
    fs::path getLogDirectory();
    
    // Get application cache directory
    fs::path getCacheDirectory();
    
    // Create directory if it doesn't exist
    bool ensureDirectoryExists(const fs::path& directory);
    
    // Get default configuration file path
    fs::path getDefaultConfigPath();
    
    // Backup configuration file
    bool backupConfigFile(const fs::path& configPath);
    
    // Restore configuration from backup
    bool restoreConfigFromBackup(const fs::path& configPath);
}

// Default configuration keys
namespace ConfigKeys {
    // General settings
    extern const std::string APP_NAME;
    extern const std::string APP_VERSION;
    extern const std::string LANGUAGE;
    extern const std::string THEME;
    extern const std::string AUTO_SAVE;
    extern const std::string CHECK_UPDATES;
    
    // Deployment settings
    extern const std::string DEFAULT_TIMEOUT;
    extern const std::string MAX_RETRIES;
    extern const std::string RETRY_DELAY;
    extern const std::string CLEANUP_ON_SUCCESS;
    extern const std::string CLEANUP_ON_FAILURE;
    extern const std::string DEPLOYMENT_MODE;
    extern const std::string WORKING_DIRECTORY;
    extern const std::string RUNNER_EXECUTABLE;
    
    // Network settings
    extern const std::string NETWORK_TIMEOUT;
    extern const std::string PING_TIMEOUT;
    extern const std::string SCAN_THREADS;
    extern const std::string DEFAULT_PORTS;
    extern const std::string USE_WMI;
    extern const std::string USE_POWERSHELL_REMOTING;
    
    // Security settings
    extern const std::string ENCRYPT_CREDENTIALS;
    extern const std::string CREDENTIAL_TIMEOUT;
    extern const std::string ENABLE_LAPS;
    extern const std::string LAPS_ATTRIBUTE;
    extern const std::string REQUIRE_ADMIN;
    extern const std::string AUDIT_DEPLOYMENTS;
    
    // GUI settings
    extern const std::string WINDOW_WIDTH;
    extern const std::string WINDOW_HEIGHT;
    extern const std::string WINDOW_MAXIMIZED;
    extern const std::string SHOW_TOOLBAR;
    extern const std::string SHOW_STATUS_BAR;
    extern const std::string REFRESH_INTERVAL;
    extern const std::string GRID_LINES;
    
    // Logging settings
    extern const std::string LOG_LEVEL;
    extern const std::string LOG_FILE_PATH;
    extern const std::string MAX_LOG_SIZE;
    extern const std::string LOG_RETENTION_DAYS;
    extern const std::string ENABLE_CONSOLE_LOG;
    extern const std::string ENABLE_FILE_LOG;
    
    // Advanced settings
    extern const std::string DEBUG_MODE;
    extern const std::string PERFORMANCE_MONITORING;
    extern const std::string MEMORY_LIMIT;
    extern const std::string THREAD_POOL_SIZE;
    extern const std::string CACHE_SIZE;
    extern const std::string TEMP_CLEANUP_INTERVAL;
}

#endif // CONFIGURATION_MANAGER_H
