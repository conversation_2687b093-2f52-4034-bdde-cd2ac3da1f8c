<doxygenlayout version="1.0">
  <!-- Generated by doxygen 1.8.14 -->
  <!-- Navigation index tabs for HTML output -->
  <navindex>
    <tab type="mainpage" visible="yes" title="Introduction"/>
    <tab type="user" url="quick_guide.html" title="Tutorial"/>
    <tab type="pages" visible="yes" title="Guides" intro=""/>
    <tab type="topics" visible="yes" title="Reference" intro=""/>
    <tab type="filelist" visible="yes" title="Files"/>
  </navindex>

  <!-- Layout definition for a file page -->
  <file>
    <detaileddescription title="Description"/>
    <includes visible="$SHOW_INCLUDE_FILES"/>
    <sourcelink visible="yes"/>
    <memberdecl>
      <constantgroups visible="yes" title=""/>
      <defines title=""/>
      <typedefs title=""/>
      <enums title=""/>
      <functions title=""/>
      <variables title=""/>
      <membergroups visible="yes"/>
    </memberdecl>
    <memberdef>
      <defines title=""/>
      <typedefs title=""/>
      <enums title=""/>
      <functions title=""/>
      <variables title=""/>
    </memberdef>
    <authorsection/>
  </file>

  <!-- Layout definition for a group page -->
  <group>
    <detaileddescription title="Description"/>
    <memberdecl>
      <nestedgroups visible="yes" title=""/>
      <dirs visible="yes" title=""/>
      <files visible="yes" title=""/>
      <defines title=""/>
      <typedefs title=""/>
      <enums title=""/>
      <enumvalues title=""/>
      <functions title=""/>
      <variables title=""/>
    </memberdecl>
    <memberdef>
      <pagedocs/>
      <defines title=""/>
      <typedefs title=""/>
      <enums title=""/>
      <enumvalues title=""/>
      <functions title=""/>
      <variables title=""/>
    </memberdef>
    <authorsection visible="yes"/>
  </group>

  <!-- Layout definition for a directory page -->
  <directory>
    <briefdescription visible="yes"/>
    <memberdecl>
      <dirs visible="yes"/>
      <files visible="yes"/>
    </memberdecl>
    <detaileddescription title=""/>
  </directory>
</doxygenlayout>
