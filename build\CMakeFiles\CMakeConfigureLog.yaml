
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.10+8b8e13593 for .NET Framework
      Build started 21/06/2025 10:20:24.
      
      Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:06.57
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/4.0.2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-fauoov"
      binary: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-fauoov"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-fauoov'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_02d71.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 21/06/2025 10:20:34.
        
        Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fauoov\\cmTC_02d71.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_02d71.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fauoov\\Debug\\".
          Creating directory "cmTC_02d71.dir\\Debug\\cmTC_02d71.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_02d71.dir\\Debug\\cmTC_02d71.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_02d71.dir\\Debug\\cmTC_02d71.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_02d71.dir\\Debug\\\\" /Fd"cmTC_02d71.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_02d71.dir\\Debug\\\\" /Fd"cmTC_02d71.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fauoov\\Debug\\cmTC_02d71.exe" /INCREMENTAL /ILK:"cmTC_02d71.dir\\Debug\\cmTC_02d71.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-fauoov/Debug/cmTC_02d71.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-fauoov/Debug/cmTC_02d71.lib" /MACHINE:X64  /machine:x64 cmTC_02d71.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_02d71.vcxproj -> C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fauoov\\Debug\\cmTC_02d71.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_02d71.dir\\Debug\\cmTC_02d71.tlog\\unsuccessfulbuild".
          Touching "cmTC_02d71.dir\\Debug\\cmTC_02d71.tlog\\cmTC_02d71.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-fauoov\\cmTC_02d71.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:05.45
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.10+8b8e13593 for .NET Framework
      Build started 21/06/2025 10:21:00.
      
      Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\4.0.2\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\4.0.2\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\4.0.2\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:05.26
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/4.0.2/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-kwe6rt"
      binary: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-kwe6rt"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-kwe6rt'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_629b4.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 21/06/2025 10:21:09.
        
        Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kwe6rt\\cmTC_629b4.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_629b4.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kwe6rt\\Debug\\".
          Creating directory "cmTC_629b4.dir\\Debug\\cmTC_629b4.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_629b4.dir\\Debug\\cmTC_629b4.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_629b4.dir\\Debug\\cmTC_629b4.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_629b4.dir\\Debug\\\\" /Fd"cmTC_629b4.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_629b4.dir\\Debug\\\\" /Fd"cmTC_629b4.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kwe6rt\\Debug\\cmTC_629b4.exe" /INCREMENTAL /ILK:"cmTC_629b4.dir\\Debug\\cmTC_629b4.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-kwe6rt/Debug/cmTC_629b4.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-kwe6rt/Debug/cmTC_629b4.lib" /MACHINE:X64  /machine:x64 cmTC_629b4.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_629b4.vcxproj -> C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kwe6rt\\Debug\\cmTC_629b4.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_629b4.dir\\Debug\\cmTC_629b4.tlog\\unsuccessfulbuild".
          Touching "cmTC_629b4.dir\\Debug\\cmTC_629b4.tlog\\cmTC_629b4.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kwe6rt\\cmTC_629b4.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:05.66
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CheckSourceCompiles.cmake:104 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:97 (check_c_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/CMakeLists.txt:60 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-adz0iz"
      binary: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-adz0iz"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/CMake/modules"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-adz0iz'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e6c72.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 21/06/2025 10:21:16.
        
        Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-adz0iz\\cmTC_e6c72.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e6c72.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-adz0iz\\Debug\\".
          Creating directory "cmTC_e6c72.dir\\Debug\\cmTC_e6c72.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e6c72.dir\\Debug\\cmTC_e6c72.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e6c72.dir\\Debug\\cmTC_e6c72.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e6c72.dir\\Debug\\\\" /Fd"cmTC_e6c72.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-adz0iz\\src.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_e6c72.dir\\Debug\\\\" /Fd"cmTC_e6c72.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-adz0iz\\src.c"
          src.c
        C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-adz0iz\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-adz0iz\\cmTC_e6c72.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-adz0iz\\cmTC_e6c72.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-adz0iz\\cmTC_e6c72.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-adz0iz\\src.c(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-adz0iz\\cmTC_e6c72.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:01.38
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/CMakeLists.txt:60 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-z8fpfj"
      binary: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-z8fpfj"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/CMake/modules"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-z8fpfj'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_a21d4.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 21/06/2025 10:21:19.
        
        Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z8fpfj\\cmTC_a21d4.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_a21d4.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z8fpfj\\Debug\\".
          Creating directory "cmTC_a21d4.dir\\Debug\\cmTC_a21d4.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_a21d4.dir\\Debug\\cmTC_a21d4.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_a21d4.dir\\Debug\\cmTC_a21d4.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a21d4.dir\\Debug\\\\" /Fd"cmTC_a21d4.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z8fpfj\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a21d4.dir\\Debug\\\\" /Fd"cmTC_a21d4.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z8fpfj\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z8fpfj\\Debug\\cmTC_a21d4.exe" /INCREMENTAL /ILK:"cmTC_a21d4.dir\\Debug\\cmTC_a21d4.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-z8fpfj/Debug/cmTC_a21d4.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-z8fpfj/Debug/cmTC_a21d4.lib" /MACHINE:X64  /machine:x64 cmTC_a21d4.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z8fpfj\\cmTC_a21d4.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z8fpfj\\cmTC_a21d4.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z8fpfj\\cmTC_a21d4.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-z8fpfj\\cmTC_a21d4.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:02.90
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CheckLibraryExists.cmake:112 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:112 (check_library_exists)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/CMakeLists.txt:60 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-6etwvj"
      binary: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-6etwvj"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS /W3"
      CMAKE_C_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/CMake/modules"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-6etwvj'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_8879c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 21/06/2025 10:21:24.
        
        Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6etwvj\\cmTC_8879c.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_8879c.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6etwvj\\Debug\\".
          Creating directory "cmTC_8879c.dir\\Debug\\cmTC_8879c.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_8879c.dir\\Debug\\cmTC_8879c.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_8879c.dir\\Debug\\cmTC_8879c.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_8879c.dir\\Debug\\\\" /Fd"cmTC_8879c.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6etwvj\\CheckFunctionExists.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_8879c.dir\\Debug\\\\" /Fd"cmTC_8879c.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TC /errorReport:queue "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6etwvj\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6etwvj\\Debug\\cmTC_8879c.exe" /INCREMENTAL /ILK:"cmTC_8879c.dir\\Debug\\cmTC_8879c.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-6etwvj/Debug/cmTC_8879c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-6etwvj/Debug/cmTC_8879c.lib" /MACHINE:X64  /machine:x64 cmTC_8879c.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6etwvj\\cmTC_8879c.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6etwvj\\cmTC_8879c.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6etwvj\\cmTC_8879c.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-6etwvj\\cmTC_8879c.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:02.81
        
      exitCode: 1
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.10+8b8e13593 for .NET Framework
      Build started 21/06/2025 13:02:02.
      
      Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\4.0.2\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:06.64
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/4.0.2/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-kr9em9"
      binary: "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-kr9em9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /W3 /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/MDd /Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-kr9em9'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_20a18.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.10+8b8e13593 for .NET Framework
        Build started 21/06/2025 13:02:12.
        
        Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kr9em9\\cmTC_20a18.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_20a18.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kr9em9\\Debug\\".
          Creating directory "cmTC_20a18.dir\\Debug\\cmTC_20a18.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_20a18.dir\\Debug\\cmTC_20a18.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_20a18.dir\\Debug\\cmTC_20a18.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_20a18.dir\\Debug\\\\" /Fd"cmTC_20a18.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35209 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W3 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_20a18.dir\\Debug\\\\" /Fd"cmTC_20a18.dir\\Debug\\vc143.pdb" /external:W3 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kr9em9\\Debug\\cmTC_20a18.exe" /INCREMENTAL /ILK:"cmTC_20a18.dir\\Debug\\cmTC_20a18.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-kr9em9/Debug/cmTC_20a18.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/CMakeFiles/CMakeScratch/TryCompile-kr9em9/Debug/cmTC_20a18.lib" /MACHINE:X64  /machine:x64 cmTC_20a18.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_20a18.vcxproj -> C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kr9em9\\Debug\\cmTC_20a18.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_20a18.dir\\Debug\\cmTC_20a18.tlog\\unsuccessfulbuild".
          Touching "cmTC_20a18.dir\\Debug\\cmTC_20a18.tlog\\cmTC_20a18.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive - Concentrix Corporation\\Desktop\\Apps-jules_wip_7542290459048296198\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kr9em9\\cmTC_20a18.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:04.27
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:270 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35209.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
