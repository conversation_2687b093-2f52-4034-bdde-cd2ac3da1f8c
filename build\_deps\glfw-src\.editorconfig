# EditorConfig for GLFW and its internal dependencies
#
# All files created by GLFW should indent with four spaces unless their format requires
# otherwise.  A few files still use other indent styles for historical reasons.
#
# Dependencies have (what seemed to be) their existing styles described.  Those with
# existing trailing whitespace have it preserved to avoid cluttering future commits.

root = true

[*]
charset = utf-8
end_of_line = lf

[include/GLFW/*.h]
indent_style = space
indent_size = 4

[{src,examples,tests}/*.{c,m,h,rc,in}]
indent_style = space
indent_size = 4

[CMakeLists.txt]
indent_style = space
indent_size = 4

[CMake/**.{cmake,in}]
indent_style = space
indent_size = 4

[*.{md}]
indent_style = space
indent_size = 4
trim_trailing_whitespace = false

[DoxygenLayout.xml]
indent_style = space
indent_size = 2

[docs/*.{scss,html}]
indent_style = tab
indent_size = unset

[deps/mingw/*.h]
indent_style = space
indent_size = 4
tab_width = 8
trim_trailing_whitespace = false

[deps/getopt.{c,h}]
indent_style = space
indent_size = 2

[deps/linmath.h]
indent_style = tab
tab_width = 4
indent_size = 4
trim_trailing_whitespace = false

[deps/nuklear*.h]
indent_style = space
indent_size = 4

[deps/tinycthread.{c,h}]
indent_style = space
indent_size = 2

