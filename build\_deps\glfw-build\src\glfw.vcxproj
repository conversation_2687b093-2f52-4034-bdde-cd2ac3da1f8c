﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5F30D296-5C51-39BD-BE92-0044D4C3D4C8}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>glfw</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">glfw.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">glfw3</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">glfw.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">glfw3</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">glfw.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">glfw3</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">glfw.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">glfw3</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_GLFW_WIN32;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_GLFW_WIN32;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_GLFW_WIN32;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_GLFW_WIN32;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_GLFW_WIN32;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_GLFW_WIN32;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>
      </ExceptionHandling>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_GLFW_WIN32;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_GLFW_WIN32;UNICODE;_UNICODE;_CRT_SECURE_NO_WARNINGS;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src;C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/xdeploy" "-BC:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build" --check-stamp-file "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-build/src/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\CMake\glfw3.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/xdeploy" "-BC:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build" --check-stamp-file "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-build/src/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\CMake\glfw3.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/xdeploy" "-BC:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build" --check-stamp-file "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-build/src/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\CMake\glfw3.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-src/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/xdeploy" "-BC:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build" --check-stamp-file "C:/Users/<USER>/OneDrive - Concentrix Corporation/Desktop/Apps-jules_wip_7542290459048296198/build/_deps/glfw-build/src/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\CMake\glfw3.pc.in;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-build\src\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include\GLFW\glfw3.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\include\GLFW\glfw3native.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\internal.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\platform.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\mappings.h" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\context.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\init.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\input.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\monitor.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\platform.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\vulkan.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\window.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\egl_context.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\osmesa_context.c" />
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\null_platform.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\null_joystick.h" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\null_init.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\null_monitor.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\null_window.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\null_joystick.c" />
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_time.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_thread.h" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_module.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_time.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_thread.c" />
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_platform.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_joystick.h" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_init.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_joystick.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_monitor.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\win32_window.c" />
    <ClCompile Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\_deps\glfw-src\src\wgl_context.c" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\OneDrive - Concentrix Corporation\Desktop\Apps-jules_wip_7542290459048296198\build\ZERO_CHECK.vcxproj">
      <Project>{C01076FF-0F1F-31C6-8371-36F28498E0D5}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>